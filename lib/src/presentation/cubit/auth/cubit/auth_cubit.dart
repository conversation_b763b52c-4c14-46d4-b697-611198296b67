import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:neorevv/src/data/repository/auth_data_repository.dart';
import '../../../../core/services/exceptions.dart';
import '/src/domain/repository/auth_repository.dart';
import '../../../../domain/models/user.dart';
part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(this._authRepository) : super(AuthInitial());

  final AuthRepository _authRepository;

  Future<void> login(Map<String, dynamic> payload) async {
    emit(AuthLoading(true));
    try {
      final result = await _authRepository.login(payload);

      // final sessionManager = SessionManager();
      // await sessionManager.saveSession(result);

      final authRepository = AuthDataRepository();
      authRepository.setTokens(result.jwt, result.refreshToken);

      emit(AuthSuccess());
    } on InvalidCredentialsException catch (e) {
      emit(AuthError(error: e.message, invalidCredentials: true));
    } catch (e) {
      emit(AuthError(error: e.toString(), invalidCredentials: false));
    }
  }
}
