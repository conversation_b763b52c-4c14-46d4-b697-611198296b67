import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'src/presentation/cubit/filter/filter_cubit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:neorevv/src/domain/repository/get_infocard_repository.dart';
import 'package:neorevv/src/domain/repository/get_network_item_repository.dart';
import 'package:neorevv/src/presentation/cubit/cubit/agent_network_cubit.dart';
import 'package:neorevv/src/presentation/cubit/infocard/cubit/infocard_cubit.dart';
import 'package:path_provider/path_provider.dart';
import 'src/core/config/constants.dart';
import '/src/domain/repository/auth_repository.dart';
import 'src/core/navigation/web_router.dart';
import 'src/core/config/app_strings.dart';
import 'src/core/services/locator.dart';
import 'src/domain/repository/filter_repository.dart';
import 'src/domain/repository/sales_details_repository.dart';
import 'src/domain/repository/broker_register_repository.dart';
import 'src/domain/repository/agent_repository.dart';
import 'src/domain/repository/broker_repository.dart';
import 'src/domain/repository/filter_repository.dart';
import 'src/domain/repository/user_repository.dart';
import 'src/domain/repository/top_performers_repository.dart';
import 'src/presentation/cubit/agent/agent_cubit.dart';
import 'src/presentation/cubit/auth/cubit/auth_cubit.dart';
import 'src/core/theme/app_theme.dart';
import 'src/presentation/cubit/filter/filter_cubit.dart';
import 'src/presentation/cubit/sales_details/sales_details_cubit.dart';
import 'src/presentation/cubit/broker_register/broker_register_cubit.dart';
import 'src/presentation/cubit/broker/broker_cubit.dart';
import 'src/presentation/cubit/filter/filter_cubit.dart';
import 'src/presentation/cubit/user/user_cubit.dart';
import 'src/presentation/cubit/top_performers/top_performers_cubit.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load env file
  await dotenv.load(fileName: ".env");

  await initializeDependencies();
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: kIsWeb
        ? HydratedStorageDirectory.web
        : HydratedStorageDirectory((await getTemporaryDirectory()).path),
  );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => AuthCubit(locator<AuthRepository>())),
        BlocProvider(create: (context) => UserCubit(locator<UserRepository>())),
        BlocProvider(
          create: (context) =>
              BrokerRegisterCubit(locator<BrokerRegisterRepository>()),
        ),

        BlocProvider(
          create: (context) =>
              TopPerformersCubit(locator<TopPerformersRepository>()),
        ),
        BlocProvider(
          create: (context) => BrokerCubit(locator<BrokerRepository>()),
        ),
        BlocProvider(
          create: (context) => DashboardCubit(locator<GetInfocardRepository>()),
        ),
        BlocProvider(
          create: (context) =>
              AgentNetworkCubit(locator<GetNetworkItemRepository>()),
        ),
        BlocProvider(
          create: (context) =>
              SalesDetailsCubit(locator<SalesDetailsRepository>()),
        ),
        BlocProvider(
          create: (context) => AgentCubit(locator<AgentRepository>()),
        ),
        BlocProvider(
          create: (context) => FilterCubit(locator<FilterRepository>()),
        ),
      ],
      child: MaterialApp.router(
        routerConfig: appRouter,
        debugShowCheckedModeBanner: false,
        title: appName,
        theme: ThemeData(
          fontFamily: fontFamily,
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppTheme.primaryColor,
            brightness: Brightness.light,
          ),

          scaffoldBackgroundColor: AppTheme.scaffoldBgColor,
        ),
        // TODO: Remove after completing go_router setup
        // home: MainLayoutScreen(),
        // routes: {
        //   '/dashboard': (context) => const DashboardScreen(),
        //   '/register-broker': (context) => RegisterBrokerScreen(),
        //   '/sale-review-doc': (context) => SalesReviewDocScreen(),
        // },
      ),
    );
  }
}
